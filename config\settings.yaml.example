# Gakumasu-Bot 系统设置配置文件
# 请复制此文件为 settings.yaml 并根据实际情况修改配置

# 系统基础设置
system:
  # 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
  log_level: INFO
  
  # 游戏语言设置: ja (日语), cn (中文)
  language: ja
  
  # 屏幕分辨率 [宽度, 高度]
  screen_resolution: [1920, 1080]
  
  # 游戏窗口标题（用于窗口识别）
  game_window_title: "gakumas"

# DMM Player 相关设置
dmm:
  # DMM Player 可执行文件路径（必须配置）
  # 示例: "C:/Users/<USER>/AppData/Local/DMM Game Player/DMMGamePlayer.exe"
  dmm_player_path: ""
  
  # 游戏图标模板文件路径
  game_icon_template: "assets/templates/dmm_gakumasu_icon.png"
  
  # 启动超时时间（秒）
  launch_timeout: 60

# 性能相关设置
performance:
  # 屏幕截图间隔（秒）
  screenshot_interval: 0.5
  
  # 操作延迟范围（秒）
  action_delay_min: 0.05
  action_delay_max: 0.15
  
  # 决策超时时间（秒）
  decision_timeout: 30.0
  
  # 是否启用GPU加速（需要CUDA支持）
  enable_gpu_acceleration: false

# AI 决策相关设置
ai:
  # 是否启用MCTS算法
  enable_mcts: true
  
  # MCTS迭代次数
  mcts_iterations: 1000
  
  # MCTS超时时间（秒）
  mcts_timeout: 10.0
  
  # 启发式权重配置
  heuristic_weights:
    score: 1.0      # 分数权重
    stamina: 0.8    # 体力权重
    vigor: 0.6      # 元气权重
    card_synergy: 0.4  # 卡牌协同权重

# OCR 识别设置
ocr:
  # 支持的语言列表
  languages: ['ja', 'en']
  
  # OCR置信度阈值
  confidence_threshold: 0.7
  
  # 文本预处理选项
  preprocessing:
    enable_denoising: true
    enable_contrast_enhancement: true

# 模板匹配设置
template_matching:
  # 匹配置信度阈值
  confidence_threshold: 0.8
  
  # 是否启用多尺度匹配
  enable_multi_scale: true
  
  # 尺度范围
  scale_range: [0.8, 1.2]

# 错误处理设置
error_handling:
  # 最大重试次数
  max_retries: 3
  
  # 重试间隔（秒）
  retry_interval: 2.0
  
  # 是否启用自动恢复
  enable_auto_recovery: true

# 调试设置
debug:
  # 是否保存调试截图
  save_debug_screenshots: false
  
  # 调试截图保存目录
  debug_screenshots_dir: "logs/debug_screenshots"
  
  # 是否启用详细日志
  enable_verbose_logging: false
