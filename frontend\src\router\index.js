import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue')
  },
  {
    path: '/screenshot-tool',
    name: 'ScreenshotTool',
    component: () => import('../views/ScreenshotTool.vue'),
    meta: {
      title: '截图工具',
      icon: 'Camera',
      description: '游戏截图数据收集工具'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
