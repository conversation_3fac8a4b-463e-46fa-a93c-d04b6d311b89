"""
Gakumasu-Bot 核心数据结构定义
基于设计文档第5.2.1节的数据模型类设计
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Dict, Any, Optional, Union
from enum import Enum


class ActionType(Enum):
    """行动类型枚举"""
    CLICK = "click"
    KEYPRESS = "keypress"
    SCROLL = "scroll"
    DRAG = "drag"


class CardType(Enum):
    """卡牌类型枚举"""
    VOCAL = "Vocal"
    DANCE = "Dance"
    VISUAL = "Visual"
    MENTAL = "Mental"


class CardRarity(Enum):
    """卡牌稀有度枚举"""
    N = "N"
    R = "R"
    SR = "SR"
    SSR = "SSR"


class GameScene(Enum):
    """游戏场景枚举"""
    MAIN_MENU = "main_menu"
    PRODUCE_SETUP = "produce_setup"
    PRODUCE_MAIN = "produce_main"
    PRODUCE_BATTLE = "produce_battle"
    PRODUCE_EXAM = "produce_exam"
    PRODUCE_RESULT = "produce_result"
    PART_TIME_JOB = "part_time_job"
    DAILY_TASKS = "daily_tasks"
    UNKNOWN = "unknown"


@dataclass
class CardEffect:
    """卡牌效果数据结构"""
    type: str  # 效果类型，如 "add_score", "add_vigor" 等
    value: Union[int, str]  # 效果数值
    duration: Optional[int] = None  # 持续时间（回合数）
    condition: Optional[str] = None  # 触发条件


@dataclass
class Card:
    """卡牌数据结构"""
    card_id: str  # 卡牌唯一标识
    name_jp: str  # 日文名称（主要匹配键）
    name_cn: Optional[str] = None  # 中文名称（可选）
    card_type: CardType = CardType.VOCAL  # 卡牌类型
    rarity: CardRarity = CardRarity.N  # 稀有度
    cost: int = 0  # 消耗成本
    effects: List[CardEffect] = field(default_factory=list)  # 卡牌效果列表
    
    def __post_init__(self):
        """后处理：确保effects是CardEffect对象列表"""
        if self.effects:
            processed_effects = []
            for effect in self.effects:
                if isinstance(effect, dict):
                    processed_effects.append(CardEffect(**effect))
                elif isinstance(effect, CardEffect):
                    processed_effects.append(effect)
            self.effects = processed_effects

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'card_id': self.card_id,
            'name_jp': self.name_jp,
            'name_cn': self.name_cn,
            'card_type': self.card_type.value,
            'rarity': self.rarity.value,
            'cost': self.cost,
            'effects': [
                {
                    'type': effect.type,
                    'value': effect.value,
                    'duration': effect.duration,
                    'condition': effect.condition
                }
                for effect in self.effects
            ]
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Card':
        """从字典创建Card对象"""
        effects = []
        for effect_data in data.get('effects', []):
            effects.append(CardEffect(**effect_data))

        return cls(
            card_id=data['card_id'],
            name_jp=data['name_jp'],
            name_cn=data.get('name_cn'),
            card_type=CardType(data.get('card_type', 'vocal')),
            rarity=CardRarity(data.get('rarity', 'N')),
            cost=data.get('cost', 0),
            effects=effects
        )


@dataclass
class GameState:
    """游戏状态数据结构"""
    current_scene: GameScene = GameScene.UNKNOWN  # 当前场景
    current_language: str = 'ja'  # 当前语言设置 ('ja' 或 'cn')
    timestamp: datetime = field(default_factory=datetime.now)  # 状态时间戳
    
    # 游戏资源状态
    stamina: int = 0  # 体力值
    vigor: int = 0  # 元气值
    score: int = 0  # 当前分数
    
    # 育成相关状态
    current_week: int = 0  # 当前周数
    hand: List[Card] = field(default_factory=list)  # 手牌
    deck_size: int = 0  # 牌组剩余数量
    discard_size: int = 0  # 弃牌堆数量
    
    # 偶像属性
    idol_stats: Dict[str, int] = field(default_factory=lambda: {
        "vocal": 0, "dance": 0, "visual": 0, "mental": 0
    })
    
    # 状态效果
    active_buffs: Dict[str, int] = field(default_factory=dict)  # 激活的增益效果
    active_debuffs: Dict[str, int] = field(default_factory=dict)  # 激活的减益效果
    
    # UI相关信息
    ui_elements: Dict[str, Any] = field(default_factory=dict)  # 识别到的UI元素位置
    screen_resolution: tuple = (1920, 1080)  # 屏幕分辨率
    
    def is_valid(self) -> bool:
        """检查游戏状态是否有效"""
        return (
            self.current_scene != GameScene.UNKNOWN and
            self.stamina >= 0 and
            self.vigor >= 0
        )

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'current_scene': self.current_scene.value,
            'current_language': self.current_language,
            'timestamp': self.timestamp.isoformat(),
            'stamina': self.stamina,
            'vigor': self.vigor,
            'score': self.score,
            'current_week': self.current_week,
            'hand': [card.to_dict() for card in self.hand],
            'deck_size': self.deck_size,
            'discard_size': self.discard_size,
            'idol_stats': self.idol_stats,
            'active_buffs': self.active_buffs,
            'active_debuffs': self.active_debuffs,
            'ui_elements': self.ui_elements,
            'screen_resolution': self.screen_resolution
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GameState':
        """从字典创建GameState对象"""
        from datetime import datetime

        # 处理手牌
        hand = []
        for card_data in data.get('hand', []):
            if isinstance(card_data, dict):
                hand.append(Card.from_dict(card_data))

        return cls(
            current_scene=GameScene(data.get('current_scene', 'unknown')),
            current_language=data.get('current_language', 'ja'),
            timestamp=datetime.fromisoformat(data.get('timestamp', datetime.now().isoformat())),
            stamina=data.get('stamina', 0),
            vigor=data.get('vigor', 0),
            score=data.get('score', 0),
            current_week=data.get('current_week', 0),
            hand=hand,
            deck_size=data.get('deck_size', 0),
            discard_size=data.get('discard_size', 0),
            idol_stats=data.get('idol_stats', {"vocal": 0, "dance": 0, "visual": 0, "mental": 0}),
            active_buffs=data.get('active_buffs', {}),
            active_debuffs=data.get('active_debuffs', {}),
            ui_elements=data.get('ui_elements', {}),
            screen_resolution=tuple(data.get('screen_resolution', (1920, 1080)))
        )


@dataclass
class Action:
    """行动指令数据结构"""
    action_type: ActionType  # 行动类型
    target: Any  # 目标（坐标、键名等）
    description: str = ""  # 行动描述
    delay_before: float = 0.1  # 执行前延迟（秒）
    delay_after: float = 0.1  # 执行后延迟（秒）
    verify_func: Optional[callable] = None  # 验证函数
    retry_count: int = 3  # 重试次数
    
    def __post_init__(self):
        """后处理：设置默认描述"""
        if not self.description:
            self.description = f"{self.action_type.value} on {self.target}"


@dataclass
class ProduceGoal:
    """育成目标配置"""
    target: str = "high_score"  # 目标类型：high_score, true_end, stat_focus
    focus_stat: Optional[str] = None  # 专注属性（当target为stat_focus时）
    target_score: Optional[int] = None  # 目标分数
    priority_weights: Dict[str, float] = field(default_factory=lambda: {
        "vocal": 1.0, "dance": 1.0, "visual": 1.0, "mental": 0.8
    })


@dataclass
class TeamComposition:
    """队伍配置"""
    produce_idol: str = ""  # 要培养的偶像名称
    support_cards: List[str] = field(default_factory=list)  # 支援卡名称列表
    deck_strategy: str = "balanced"  # 牌组策略：balanced, aggressive, defensive


@dataclass
class BehaviorConfig:
    """行为配置"""
    risk_aversion: float = 0.5  # 风险规避程度 (0.0-1.0)
    stamina_management_style: str = "balanced"  # 体力管理风格
    decision_timeout: float = 30.0  # 决策超时时间（秒）
    enable_mcts: bool = True  # 是否启用MCTS算法
    mcts_iterations: int = 1000  # MCTS迭代次数


@dataclass
class UserStrategy:
    """用户策略配置"""
    produce_goal: ProduceGoal = field(default_factory=ProduceGoal)
    team_composition: TeamComposition = field(default_factory=TeamComposition)
    behavior: BehaviorConfig = field(default_factory=BehaviorConfig)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserStrategy':
        """从字典创建UserStrategy对象"""
        produce_goal_data = data.get('produce_goal', {})
        team_composition_data = data.get('team_composition', {})
        behavior_data = data.get('behavior', {})
        
        return cls(
            produce_goal=ProduceGoal(**produce_goal_data),
            team_composition=TeamComposition(**team_composition_data),
            behavior=BehaviorConfig(**behavior_data)
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'produce_goal': {
                'target': self.produce_goal.target,
                'focus_stat': self.produce_goal.focus_stat,
                'target_score': self.produce_goal.target_score,
                'priority_weights': self.produce_goal.priority_weights
            },
            'team_composition': {
                'produce_idol': self.team_composition.produce_idol,
                'support_cards': self.team_composition.support_cards,
                'deck_strategy': self.team_composition.deck_strategy
            },
            'behavior': {
                'risk_aversion': self.behavior.risk_aversion,
                'stamina_management_style': self.behavior.stamina_management_style,
                'decision_timeout': self.behavior.decision_timeout,
                'enable_mcts': self.behavior.enable_mcts,
                'mcts_iterations': self.behavior.mcts_iterations
            }
        }


# 异常类定义
class GakumasuBotException(Exception):
    """Gakumasu-Bot基础异常类"""
    pass


class GameUIElementNotFound(GakumasuBotException):
    """游戏UI元素未找到异常"""
    pass


class GameCrashException(GakumasuBotException):
    """游戏崩溃异常"""
    pass


class OCRUnreliableException(GakumasuBotException):
    """OCR识别不可靠异常"""
    pass


class InvalidConfiguration(GakumasuBotException):
    """无效配置异常"""
    pass


class DMMPlayerLaunchFailed(GakumasuBotException):
    """DMM Player启动失败异常"""
    pass


class GameIconNotFoundInDMM(GakumasuBotException):
    """DMM中未找到游戏图标异常"""
    pass


class NoValidActionFound(GakumasuBotException):
    """未找到有效行动异常"""
    pass


class InputSimulationError(GakumasuBotException):
    """输入模拟错误异常"""
    pass
