"""
游戏任务类定义
包含各种游戏相关的自动化任务
"""

import time
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from enum import Enum

from ...utils.logger import get_logger
from ...core.data_structures import (
    GameState, GameScene, Action, ActionType, 
    GakumasuBotException, GameUIElementNotFound
)
from .task_manager import Task, TaskStatus, TaskPriority


class ProducePhase(Enum):
    """育成阶段枚举"""
    PREPARATION = "preparation"      # 准备阶段
    TEAM_SELECTION = "team_selection"  # 队伍选择
    MAIN_LOOP = "main_loop"         # 主循环
    MIDTERM_EXAM = "midterm_exam"   # 中期考试
    FINAL_EXAM = "final_exam"       # 最终考试
    SETTLEMENT = "settlement"       # 结算
    COMPLETED = "completed"         # 完成


class ProduceTask(Task):
    """完整育成流程任务"""
    
    def __init__(self, 
                 perception_module=None,
                 decision_module=None,
                 action_controller=None,
                 user_strategy: Optional[Dict[str, Any]] = None,
                 **kwargs):
        """
        初始化育成任务
        
        Args:
            perception_module: 感知模块
            decision_module: 决策模块
            action_controller: 行动控制器
            user_strategy: 用户策略配置
            **kwargs: 其他任务参数
        """
        super().__init__(
            name="完整育成流程",
            description="执行一次完整的偶像育成流程",
            priority=TaskPriority.HIGH,
            timeout=3600,  # 1小时超时
            max_retries=1,  # 育成任务只重试一次
            **kwargs
        )
        
        self.logger = get_logger("ProduceTask")
        
        # 模块引用
        self.perception = perception_module
        self.decision = decision_module
        self.action = action_controller
        
        # 配置
        self.user_strategy = user_strategy or {}
        
        # 状态跟踪
        self.current_phase = ProducePhase.PREPARATION
        self.current_week = 0
        self.produce_result = None
        
        # 执行统计
        self.phase_start_times = {}
        self.phase_durations = {}
        self.weekly_actions = []
        self.exam_results = []
        
        # 设置执行函数
        self.execute_func = self._execute_produce_workflow
    
    def _execute_produce_workflow(self) -> Dict[str, Any]:
        """
        执行完整育成工作流程
        
        Returns:
            育成结果字典
        """
        self.logger.info("开始执行完整育成流程")
        
        try:
            # 阶段1：准备育成
            self._execute_phase(ProducePhase.PREPARATION, self._prepare_produce)
            
            # 阶段2：选择队伍
            self._execute_phase(ProducePhase.TEAM_SELECTION, self._select_team_composition)
            
            # 阶段3：育成主循环
            self._execute_phase(ProducePhase.MAIN_LOOP, self._execute_produce_main_loop)
            
            # 阶段4：中期考试
            self._execute_phase(ProducePhase.MIDTERM_EXAM, self._handle_midterm_exam)
            
            # 阶段5：最终考试
            self._execute_phase(ProducePhase.FINAL_EXAM, self._handle_final_exam)
            
            # 阶段6：结算
            self._execute_phase(ProducePhase.SETTLEMENT, self._complete_settlement)
            
            # 完成
            self.current_phase = ProducePhase.COMPLETED
            
            # 生成结果报告
            result = self._generate_produce_report()
            self.produce_result = result
            
            self.logger.info("育成流程执行完成")
            return result
            
        except Exception as e:
            self.logger.error(f"育成流程执行失败: {e}")
            raise GakumasuBotException(f"育成任务失败: {e}")
    
    def _execute_phase(self, phase: ProducePhase, phase_func):
        """
        执行育成阶段
        
        Args:
            phase: 阶段类型
            phase_func: 阶段执行函数
        """
        self.current_phase = phase
        self.phase_start_times[phase] = datetime.now()
        
        self.logger.info(f"开始执行阶段: {phase.value}")
        
        try:
            phase_func()
            duration = datetime.now() - self.phase_start_times[phase]
            self.phase_durations[phase] = duration.total_seconds()
            
            self.logger.info(f"阶段 {phase.value} 完成，耗时: {duration.total_seconds():.1f}秒")
            
        except Exception as e:
            self.logger.error(f"阶段 {phase.value} 执行失败: {e}")
            raise
    
    def _prepare_produce(self):
        """准备育成阶段"""
        self.logger.info("准备育成阶段")
        
        # 1. 检查游戏状态
        game_state = self._get_current_game_state()
        
        # 2. 导航到育成准备界面
        self._navigate_to_produce_setup()
        
        # 3. 检查资源（体力等）
        self._check_resources()
        
        self.logger.info("育成准备完成")
    
    def _select_team_composition(self):
        """选择队伍配置"""
        self.logger.info("选择队伍配置")
        
        # 1. 选择偶像
        self._select_produce_idol()
        
        # 2. 选择支援卡
        self._select_support_cards()
        
        # 3. 确认配置
        self._confirm_team_setup()
        
        self.logger.info("队伍配置完成")
    
    def _execute_produce_main_loop(self):
        """执行育成主循环"""
        self.logger.info("开始育成主循环")
        
        # 育成通常是12周
        for week in range(1, 13):
            self.current_week = week
            self.logger.info(f"执行第 {week} 周")
            
            try:
                # 执行每周行动
                action_result = self._execute_weekly_action(week)
                self.weekly_actions.append({
                    'week': week,
                    'action': action_result,
                    'timestamp': datetime.now()
                })
                
                # 处理每周事件
                self._handle_weekly_events()
                
                # 检查是否需要中期考试
                if week == 6:  # 通常第6周有中期考试
                    break
                
            except Exception as e:
                self.logger.error(f"第 {week} 周执行失败: {e}")
                # 可以选择继续或中断
                if self._should_continue_after_error(e):
                    continue
                else:
                    raise
        
        self.logger.info("育成主循环完成")
    
    def _execute_weekly_action(self, week: int) -> Dict[str, Any]:
        """
        执行每周行动
        
        Args:
            week: 周数
            
        Returns:
            行动结果
        """
        # 获取当前游戏状态
        game_state = self._get_current_game_state()
        
        # 使用决策模块选择行动
        if self.decision:
            action = self.decision.decide_weekly_action(game_state, week, self.user_strategy)
        else:
            # 默认行动选择逻辑
            action = self._default_weekly_action(game_state, week)
        
        # 执行行动
        if self.action:
            success = self.action.execute_and_verify(action)
            if not success:
                raise GakumasuBotException(f"第 {week} 周行动执行失败")
        
        return {
            'action_type': action.action_type.value if action else 'unknown',
            'description': action.description if action else 'default action',
            'success': success if 'success' in locals() else True
        }
    
    def _handle_weekly_events(self):
        """处理每周事件"""
        # 检查是否有随机事件
        game_state = self._get_current_game_state()
        
        # 如果检测到事件界面，使用决策模块处理
        if game_state.current_scene == GameScene.PRODUCE_EVENT:
            if self.decision:
                event_action = self.decision.handle_produce_event(game_state)
                if event_action and self.action:
                    self.action.execute_and_verify(event_action)
    
    def _handle_midterm_exam(self):
        """处理中期考试"""
        self.logger.info("处理中期考试")
        
        # 检查是否进入考试界面
        game_state = self._get_current_game_state()
        
        if game_state.current_scene == GameScene.PRODUCE_EXAM:
            exam_result = self._execute_exam_sequence()
            self.exam_results.append({
                'type': 'midterm',
                'result': exam_result,
                'timestamp': datetime.now()
            })
        
        self.logger.info("中期考试完成")
    
    def _handle_final_exam(self):
        """处理最终考试"""
        self.logger.info("处理最终考试")
        
        # 继续剩余周数的育成
        for week in range(7, 13):  # 从第7周到第12周
            self.current_week = week
            self.logger.info(f"执行第 {week} 周")
            
            action_result = self._execute_weekly_action(week)
            self.weekly_actions.append({
                'week': week,
                'action': action_result,
                'timestamp': datetime.now()
            })
            
            self._handle_weekly_events()
        
        # 执行最终考试
        game_state = self._get_current_game_state()
        if game_state.current_scene == GameScene.PRODUCE_EXAM:
            exam_result = self._execute_exam_sequence()
            self.exam_results.append({
                'type': 'final',
                'result': exam_result,
                'timestamp': datetime.now()
            })
        
        self.logger.info("最终考试完成")
    
    def _complete_settlement(self):
        """完成结算"""
        self.logger.info("处理育成结算")
        
        # 等待结算界面
        self._wait_for_scene(GameScene.PRODUCE_RESULT, timeout=30)
        
        # 获取育成结果
        game_state = self._get_current_game_state()
        
        # 点击确认按钮完成结算
        if self.action:
            confirm_action = Action(
                action_type=ActionType.CLICK,
                target=(960, 800),  # 确认按钮位置
                description="确认育成结果"
            )
            self.action.execute_and_verify(confirm_action)
        
        # 返回主菜单
        self._navigate_to_main_menu()
        
        self.logger.info("育成结算完成")
    
    def _get_current_game_state(self) -> GameState:
        """获取当前游戏状态"""
        if not self.perception:
            raise GakumasuBotException("感知模块未初始化")
        
        return self.perception.get_game_state()
    
    def _generate_produce_report(self) -> Dict[str, Any]:
        """生成育成报告"""
        total_duration = sum(self.phase_durations.values())
        
        return {
            'task_id': self.task_id,
            'start_time': self.started_at,
            'end_time': self.completed_at,
            'total_duration': total_duration,
            'phase_durations': self.phase_durations,
            'weekly_actions': self.weekly_actions,
            'exam_results': self.exam_results,
            'final_phase': self.current_phase.value,
            'success': self.current_phase == ProducePhase.COMPLETED
        }
    
    # 辅助方法实现
    def _navigate_to_produce_setup(self):
        """导航到育成准备界面"""
        self.logger.debug("导航到育成准备界面")

        # 等待主菜单
        self._wait_for_scene(GameScene.MAIN_MENU, timeout=10)

        # 点击育成按钮
        if self.action:
            produce_button_action = Action(
                action_type=ActionType.CLICK,
                target=(500, 400),  # 育成按钮位置（需要根据实际UI调整）
                description="点击育成按钮"
            )
            self.action.execute_and_verify(produce_button_action)

        # 等待育成准备界面
        self._wait_for_scene(GameScene.PRODUCE_SETUP, timeout=15)

    def _check_resources(self):
        """检查资源状态"""
        game_state = self._get_current_game_state()

        # 检查体力
        if game_state.stamina < 30:  # 假设育成需要30体力
            raise GakumasuBotException(f"体力不足: {game_state.stamina}/30")

        self.logger.info(f"资源检查通过 - 体力: {game_state.stamina}, 元气: {game_state.vigor}")

    def _select_produce_idol(self):
        """选择育成偶像"""
        self.logger.debug("选择育成偶像")

        # 从用户策略中获取偶像选择
        target_idol = self.user_strategy.get('team_composition', {}).get('produce_idol', '')

        if target_idol:
            self.logger.info(f"选择偶像: {target_idol}")
            # 这里需要实现具体的偶像选择逻辑
            # 可能需要滚动列表、识别偶像名称等
        else:
            self.logger.info("使用默认偶像选择")

        # 点击确认选择
        if self.action:
            confirm_action = Action(
                action_type=ActionType.CLICK,
                target=(800, 600),  # 确认按钮位置
                description="确认偶像选择"
            )
            self.action.execute_and_verify(confirm_action)

    def _select_support_cards(self):
        """选择支援卡"""
        self.logger.debug("选择支援卡")

        # 从用户策略中获取支援卡配置
        support_cards = self.user_strategy.get('team_composition', {}).get('support_cards', [])

        if support_cards:
            self.logger.info(f"目标支援卡: {support_cards}")
            # 这里需要实现具体的支援卡选择逻辑
        else:
            self.logger.info("使用默认支援卡配置")

        # 点击确认选择
        if self.action:
            confirm_action = Action(
                action_type=ActionType.CLICK,
                target=(800, 700),  # 确认按钮位置
                description="确认支援卡选择"
            )
            self.action.execute_and_verify(confirm_action)

    def _confirm_team_setup(self):
        """确认队伍配置"""
        self.logger.debug("确认队伍配置")

        # 点击开始育成按钮
        if self.action:
            start_action = Action(
                action_type=ActionType.CLICK,
                target=(960, 800),  # 开始育成按钮位置
                description="开始育成"
            )
            self.action.execute_and_verify(start_action)

        # 等待进入育成主界面
        self._wait_for_scene(GameScene.PRODUCE_MAIN, timeout=20)

    def _default_weekly_action(self, game_state: GameState, week: int) -> Optional[Action]:
        """
        默认每周行动选择逻辑

        Args:
            game_state: 当前游戏状态
            week: 周数

        Returns:
            选择的行动
        """
        # 简单的默认策略：前期练习，后期休息
        if week <= 8:
            # 选择练习
            return Action(
                action_type=ActionType.CLICK,
                target=(400, 300),  # 练习按钮位置
                description=f"第{week}周选择练习"
            )
        else:
            # 选择休息
            return Action(
                action_type=ActionType.CLICK,
                target=(600, 300),  # 休息按钮位置
                description=f"第{week}周选择休息"
            )

    def _should_continue_after_error(self, error: Exception) -> bool:
        """
        判断错误后是否继续执行

        Args:
            error: 发生的错误

        Returns:
            是否继续执行
        """
        # 对于一些非致命错误，可以选择继续
        if isinstance(error, GameUIElementNotFound):
            self.logger.warning(f"UI元素未找到，尝试继续: {error}")
            return True

        # 其他错误则中断执行
        return False

    def _execute_exam_sequence(self) -> Dict[str, Any]:
        """
        执行考试序列

        Returns:
            考试结果
        """
        self.logger.info("执行考试序列")

        exam_start_time = datetime.now()
        total_score = 0
        turn_count = 0

        try:
            # 考试通常是多回合的卡牌战斗
            while True:
                game_state = self._get_current_game_state()

                # 检查是否还在考试界面
                if game_state.current_scene != GameScene.PRODUCE_EXAM:
                    break

                turn_count += 1
                self.logger.debug(f"考试第 {turn_count} 回合")

                # 使用决策模块选择卡牌
                if self.decision:
                    card_action = self.decision.decide_exam_card(game_state)
                    if card_action and self.action:
                        self.action.execute_and_verify(card_action)

                # 等待回合结束
                time.sleep(2)

                # 防止无限循环
                if turn_count > 20:  # 最多20回合
                    self.logger.warning("考试回合数超过限制，强制结束")
                    break

            exam_duration = (datetime.now() - exam_start_time).total_seconds()

            return {
                'duration': exam_duration,
                'turns': turn_count,
                'score': total_score,
                'success': True
            }

        except Exception as e:
            self.logger.error(f"考试执行失败: {e}")
            return {
                'duration': (datetime.now() - exam_start_time).total_seconds(),
                'turns': turn_count,
                'score': 0,
                'success': False,
                'error': str(e)
            }

    def _wait_for_scene(self, target_scene: GameScene, timeout: float = 30):
        """
        等待指定场景出现

        Args:
            target_scene: 目标场景
            timeout: 超时时间（秒）
        """
        if not self.perception:
            return

        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                game_state = self.perception.get_game_state()
                if game_state.current_scene == target_scene:
                    self.logger.debug(f"成功等到场景: {target_scene.value}")
                    return

                time.sleep(1)  # 等待1秒后重试

            except Exception as e:
                self.logger.warning(f"等待场景时出错: {e}")
                time.sleep(1)

        raise GakumasuBotException(f"等待场景超时: {target_scene.value}")

    def _navigate_to_main_menu(self):
        """导航回主菜单"""
        self.logger.debug("导航回主菜单")

        # 点击返回主菜单按钮
        if self.action:
            menu_action = Action(
                action_type=ActionType.CLICK,
                target=(100, 100),  # 菜单按钮位置
                description="返回主菜单"
            )
            self.action.execute_and_verify(menu_action)

        # 等待主菜单出现
        self._wait_for_scene(GameScene.MAIN_MENU, timeout=15)
