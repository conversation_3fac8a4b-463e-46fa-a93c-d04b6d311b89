# Gakumasu-Bot

**学园偶像大师自动化游玩程序**

[![Python Version](https://img.shields.io/badge/python-3.13+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Development Status](https://img.shields.io/badge/status-开发中-yellow.svg)]()

## 项目简介

Gakumasu-Bot 是一个专为《学园偶像大师》PC客户端设计的自动化游玩程序。通过非侵入式的计算机视觉和人工智能技术，模拟人类玩家的观察与操作，实现7x24小时无人值守的游戏托管。

### 主要特性

- 🎯 **全自动化育成流程** - 从游戏启动到育成完成的端到端自动化
- 🧠 **智能决策系统** - 集成启发式评分模型和MCTS算法
- 🌐 **多语言支持** - 优先支持日语原版，兼容中文插件
- ⚡ **高性能优化** - 基于Python 3.13的JIT编译和无GIL并发
- 🛡️ **安全可靠** - 纯视觉交互，不修改游戏文件或内存
- 📊 **可配置策略** - 支持用户自定义育成目标和行为策略

## 系统要求

### 硬件要求
- **操作系统**: Windows 10/11 (64位)
- **CPU**: Intel Core i5 或同等性能处理器
- **内存**: 推荐 16GB 或以上
- **显卡**: NVIDIA GTX 1650 或以上（用于AI计算加速）
- **存储**: 至少 2GB 可用空间

### 软件要求
- **Python**: 3.13.3+ (64位)
- **游戏客户端**: 《学园偶像大师》PC版最新版
- **DMM Player**: 最新版本
- **CUDA**: 可选，用于GPU加速

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone https://github.com/your-repo/gakumasu-bot.git
cd gakumasu-bot

# 创建虚拟环境
python -m venv venv
venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置

```bash
# 复制配置文件模板
copy config\settings.yaml.example config\settings.yaml
copy config\user_strategy.yaml.example config\user_strategy.yaml

# 编辑配置文件
notepad config\settings.yaml
notepad config\user_strategy.yaml
```

**重要**: 必须在 `settings.yaml` 中正确配置 `dmm_player_path`

### 3. 运行程序

```bash
# 启动程序
python main.py

# 调试模式
python main.py --debug
```

## 项目结构

```
Gakumasu-Bot/
├── src/                    # 源代码目录
│   ├── core/              # 核心数据结构
│   ├── modules/           # 功能模块
│   └── utils/             # 工具函数
├── config/                # 配置文件
├── data/                  # 数据文件
├── assets/                # 资源文件
│   └── templates/         # 模板图片
├── tests/                 # 测试文件
├── logs/                  # 日志文件
├── docs/                  # 文档
├── main.py               # 主程序入口
└── requirements.txt      # 依赖列表
```

## 配置说明

### 系统设置 (settings.yaml)

```yaml
# DMM Player 路径配置（必须）
dmm:
  dmm_player_path: "C:/Users/<USER>/AppData/Local/DMM Game Player/DMMGamePlayer.exe"

# 语言设置
system:
  language: ja  # ja=日语, cn=中文

# AI 决策设置
ai:
  enable_mcts: true
  mcts_iterations: 1000
```

### 用户策略 (user_strategy.yaml)

```yaml
# 育成目标
produce_goal:
  target: high_score  # high_score, true_end, stat_focus
  target_score: 15000

# 队伍配置
team_composition:
  produce_idol: "花海咲季"
  support_cards:
    - "【SSR】まだ見ぬ景色"
    - "【SR】あの日と同じように"

# 行为设置
behavior:
  risk_aversion: 0.5  # 0.0=激进, 1.0=保守
  stamina_management_style: balanced
```

## 开发状态

当前版本: **v1.0.0-alpha**

### 已完成功能
- ✅ 项目基础架构
- ✅ 核心数据结构
- ✅ 配置系统
- ✅ 日志系统

### 开发中功能
- 🚧 感知模块 (屏幕捕获、场景识别)
- 🚧 行动模块 (键鼠模拟、游戏启动)
- 🚧 决策模块 (AI算法、事件处理)
- 🚧 任务调度系统

### 计划功能
- 📋 Web管理界面
- 📋 性能监控
- 📋 云端配置同步

## 项目架构

### 系统架构概览

Gakumasu-Bot 采用模块化的四层架构设计，实现了感知-决策-行动-调度的完整自动化流程：

```
┌─────────────────────────────────────────────────────────────┐
│                    前端用户界面层                              │
├─────────────────────────────────────────────────────────────┤
│  Vue.js 3 + Element Plus + FastAPI + WebSocket             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 主控制面板   │ │ 配置管理界面 │ │ 日志查看器   │ │ 性能监控 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↕ HTTP/WebSocket
┌─────────────────────────────────────────────────────────────┐
│                    核心业务逻辑层                              │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 任务调度器   │ │ 感知模块     │ │ 决策模块     │ │ 行动模块 │ │
│  │ (Scheduler) │ │(Perception) │ │ (Decision)  │ │(Action) │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↕
┌─────────────────────────────────────────────────────────────┐
│                    数据管理层                                 │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 状态管理器   │ │ 配置管理器   │ │ 日志系统     │ │ 工具函数 │ │
│  │StateManager │ │ConfigManager│ │ Logger      │ │ Utils   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 详细目录结构

```
Gakumasu-Bot/
├── src/                           # 源代码目录
│   ├── core/                      # 核心数据结构
│   │   ├── __init__.py           # 模块导出定义
│   │   └── data_structures.py    # 核心数据类和枚举
│   ├── modules/                   # 功能模块
│   │   ├── scheduler/             # 任务调度系统
│   │   │   ├── scheduler.py      # 调度器主类
│   │   │   ├── task_manager.py   # 任务管理器
│   │   │   ├── state_manager.py  # 状态管理器
│   │   │   └── config_manager.py # 配置管理器
│   │   ├── perception/            # 感知模块
│   │   │   ├── perception_module.py    # 感知模块主类
│   │   │   ├── screen_capture.py       # 屏幕捕获
│   │   │   ├── scene_recognizer.py     # 场景识别
│   │   │   └── template_matcher.py     # 模板匹配
│   │   ├── decision/              # 决策模块
│   │   │   ├── decision_module.py      # 决策模块主类
│   │   │   ├── heuristic_evaluator.py  # 启发式评估
│   │   │   ├── mcts_engine.py          # MCTS算法引擎
│   │   │   └── event_handler.py        # 事件处理器
│   │   └── action/                # 行动模块
│   │       ├── action_controller.py    # 行动控制器
│   │       ├── input_simulator.py      # 输入模拟器
│   │       └── game_launcher.py        # 游戏启动器
│   ├── utils/                     # 工具函数
│   │   ├── config_loader.py      # 配置加载器
│   │   └── logger.py             # 日志系统
│   └── web/                       # Web API服务
│       ├── main.py               # FastAPI主程序
│       └── static/               # 静态资源
├── frontend/                      # 前端界面
│   ├── src/                      # Vue.js源码
│   │   ├── App.vue              # 主应用组件
│   │   ├── main.js              # 入口文件
│   │   ├── router/              # 路由配置
│   │   └── views/               # 页面组件
│   ├── package.json             # 前端依赖配置
│   └── vite.config.js           # 构建配置
├── config/                        # 配置文件目录
│   ├── settings.yaml.example     # 系统设置模板
│   ├── user_strategy.yaml.example # 用户策略模板
│   └── profiles/                 # 用户配置档案
├── data/                          # 数据文件目录
│   ├── cards.json.example        # 卡牌数据模板
│   ├── events.json.example       # 事件数据模板
│   ├── state.json.example        # 状态数据模板
│   └── states/                   # 状态存档
├── assets/                        # 资源文件
│   └── templates/                # 模板图片
├── tests/                         # 测试文件
├── logs/                          # 日志文件
├── docs/                          # 项目文档
├── main.py                        # 命令行主程序
├── gui.py                         # GUI启动器
└── requirements.txt               # Python依赖
```

### 核心模块功能说明

#### 1. 调度系统 (Scheduler)
**主要职责**: 整个系统的核心控制器，负责任务调度和模块协调

**核心类**:
- `Scheduler`: 调度器主类，统一管理所有模块
- `TaskManager`: 任务管理器，处理任务队列和优先级
- `StateManager`: 状态管理器，维护游戏状态持久化
- `ConfigManager`: 配置管理器，动态加载和更新配置

**关键功能**:
- 基于APScheduler的定时任务调度
- 多线程任务执行和状态同步
- 异常处理和自动恢复机制

#### 2. 感知模块 (Perception)
**主要职责**: 从游戏画面获取状态信息，识别当前场景和UI元素

**核心类**:
- `PerceptionModule`: 感知模块主类，整合各种识别功能
- `ScreenCapture`: 屏幕捕获器，支持窗口定位和区域截图
- `SceneRecognizer`: 场景识别器，基于模板匹配识别游戏场景
- `TemplateMatcher`: 模板匹配器，查找UI元素位置

**技术栈**:
- OpenCV: 图像处理和计算机视觉
- EasyOCR: 日语文字识别
- MSS: 高性能屏幕截图

#### 3. 决策模块 (Decision)
**主要职责**: 基于当前状态和用户策略，决定最优行动方案

**核心类**:
- `DecisionModule`: 决策模块主类，整合多种决策算法
- `HeuristicEvaluator`: 启发式评估器，快速评估行动价值
- `MCTSEngine`: MCTS算法引擎，深度搜索最优策略
- `EventHandler`: 事件处理器，处理特殊游戏事件

**算法特性**:
- 多层次决策：启发式 + MCTS + 规则引擎
- 支持用户自定义策略权重
- 实时学习和策略优化

#### 4. 行动模块 (Action)
**主要职责**: 执行决策结果，模拟用户输入操作

**核心类**:
- `ActionController`: 行动控制器，统一管理所有操作
- `InputSimulator`: 输入模拟器，模拟鼠标键盘操作
- `GameLauncher`: 游戏启动器，管理DMM Player和游戏进程

**技术实现**:
- PyDirectInput: 底层输入模拟
- PyWin32: Windows API调用
- 防检测机制和人性化操作模拟

### 数据流和工作流程

#### 系统启动流程
1. **配置加载**: ConfigLoader读取settings.yaml和user_strategy.yaml
2. **模块初始化**: 按依赖顺序初始化各核心模块
3. **状态恢复**: StateManager加载上次保存的游戏状态
4. **任务调度**: Scheduler开始执行预定任务

#### 核心工作循环
1. **状态感知**: PerceptionModule捕获屏幕并识别当前场景
2. **决策计算**: DecisionModule基于状态和策略计算最优行动
3. **行动执行**: ActionController执行决策结果
4. **状态更新**: StateManager更新并持久化新状态
5. **循环继续**: 返回步骤1，形成闭环控制

#### 数据流向图
```
配置文件 → ConfigManager → UserStrategy
    ↓
游戏画面 → PerceptionModule → GameState
    ↓
GameState + UserStrategy → DecisionModule → Action
    ↓
Action → ActionController → 游戏操作
    ↓
新状态 → StateManager → 状态持久化
```

### 关键API和使用示例

#### 核心数据结构

**GameState - 游戏状态**
```python
from src.core.data_structures import GameState, GameScene

# 创建游戏状态
game_state = GameState(
    current_scene=GameScene.PRODUCE_SELECTION,
    stamina=120,
    current_week=1,
    stats={"vocal": 100, "dance": 80, "visual": 90, "mental": 70}
)
```

**Action - 行动指令**
```python
from src.core.data_structures import Action, ActionType

# 创建点击行动
click_action = Action(
    action_type=ActionType.CLICK,
    target=(500, 300),  # 屏幕坐标
    description="点击确认按钮",
    delay_before=0.5,
    delay_after=1.0
)
```

#### 模块使用示例

**感知模块使用**
```python
from src.modules.perception import PerceptionModule

# 初始化感知模块
perception = PerceptionModule(
    game_window_title="gakumas",
    language="ja",
    enable_ocr=True
)

# 获取当前游戏状态
current_state = perception.get_game_state()
print(f"当前场景: {current_state.current_scene}")
print(f"当前体力: {current_state.stamina}")

# 查找UI元素
confirm_button = perception.find_ui_element("confirm_button")
if confirm_button:
    print(f"确认按钮位置: {confirm_button.center}")
```

**决策模块使用**
```python
from src.modules.decision import DecisionModule
from src.utils.config_loader import ConfigLoader

# 加载用户策略
config_loader = ConfigLoader()
user_strategy = config_loader.load_user_strategy()

# 初始化决策模块
decision = DecisionModule(user_strategy)

# 基于当前状态做决策
action = decision.make_decision(current_state)
print(f"建议行动: {action.description}")
```

**行动模块使用**
```python
from src.modules.action import ActionController

# 初始化行动控制器
action_controller = ActionController()

# 执行行动
success = action_controller.execute_action(action)
if success:
    print("行动执行成功")
else:
    print("行动执行失败")

# 启动游戏
game_launched = action_controller.launch_game()
```

**调度器使用**
```python
from src.modules.scheduler import Scheduler

# 初始化调度器
scheduler = Scheduler()

# 添加定时任务
scheduler.add_task(
    task_id="daily_produce",
    task_type="produce",
    schedule_time="09:00",
    parameters={"target_idol": "花海咲季"}
)

# 启动调度器
scheduler.start()
```

#### Web API接口

**系统状态查询**
```bash
# 获取系统状态
GET /api/v1/status

# 响应示例
{
    "status": "running",
    "current_task": "produce_training",
    "game_state": {
        "scene": "PRODUCE_SELECTION",
        "stamina": 120,
        "week": 1
    },
    "uptime": "02:30:15"
}
```

**任务管理**
```bash
# 创建新任务
POST /api/v1/tasks
{
    "task_type": "produce",
    "parameters": {
        "target_idol": "花海咲季",
        "target_score": 15000
    }
}

# 获取任务列表
GET /api/v1/tasks
```

**配置管理**
```bash
# 获取当前配置
GET /api/v1/config

# 更新配置
POST /api/v1/config
{
    "section": "ai",
    "key": "enable_mcts",
    "value": true
}
```

#### WebSocket实时通信

**前端连接示例**
```javascript
// 建立WebSocket连接
const socket = io('ws://localhost:8000');

// 监听状态更新
socket.on('status_update', (data) => {
    console.log('系统状态更新:', data);
    updateUI(data);
});

// 监听任务完成事件
socket.on('task_completed', (data) => {
    console.log('任务完成:', data.task_id);
    showNotification(`任务 ${data.task_id} 已完成`);
});

// 发送控制指令
socket.emit('control_command', {
    action: 'start_produce',
    parameters: { idol: '花海咲季' }
});
```

### 模块依赖关系

#### 依赖层次图
```
┌─────────────────┐
│   Web Frontend  │ (Vue.js + Element Plus)
└─────────────────┘
         ↓
┌─────────────────┐
│   Web API       │ (FastAPI + WebSocket)
└─────────────────┘
         ↓
┌─────────────────┐
│   Scheduler     │ ← 系统核心控制器
└─────────────────┘
    ↓    ↓    ↓
┌─────┐ ┌─────┐ ┌─────┐
│Perception│Decision│Action│ ← 核心业务模块
└─────┘ └─────┘ └─────┘
    ↓    ↓    ↓
┌─────────────────┐
│ Utils & Config  │ ← 基础工具层
└─────────────────┘
    ↓
┌─────────────────┐
│ Core Structures │ ← 数据结构层
└─────────────────┘
```

#### 模块间交互关系

**Scheduler (调度器)** - 系统中枢
- 依赖: StateManager, ConfigManager, TaskManager
- 调用: Perception, Decision, Action
- 职责: 统一调度和模块协调

**Perception (感知模块)** - 信息输入
- 依赖: ScreenCapture, SceneRecognizer, TemplateMatcher
- 被调用: Scheduler, Decision
- 职责: 游戏状态感知和场景识别

**Decision (决策模块)** - 智能决策
- 依赖: HeuristicEvaluator, MCTSEngine, EventHandler
- 调用: Perception (获取状态)
- 被调用: Scheduler
- 职责: 基于状态和策略做出最优决策

**Action (行动模块)** - 操作执行
- 依赖: InputSimulator, GameLauncher
- 调用: Perception (验证操作结果)
- 被调用: Scheduler
- 职责: 执行决策结果和游戏操作

### 系统启动方式

#### 1. 命令行模式 (开发/调试)
```bash
# 基础启动
python main.py

# 调试模式
python main.py --debug

# 指定配置文件
python main.py --config custom_settings.yaml

# 仅测试模式
python main.py --test-only
```

#### 2. GUI启动器 (推荐)
```bash
# 一键启动前后端
python gui.py

# 仅启动后端API
python gui.py --backend-only

# 仅启动前端界面
python gui.py --frontend-only

# 自定义端口
python gui.py --backend-port 8080 --frontend-port 3001
```

#### 3. Web界面访问
启动后访问: `http://localhost:3000`

**功能模块**:
- **主控制面板**: 系统状态监控和基本控制
- **配置管理**: 在线编辑系统设置和用户策略
- **任务管理**: 查看、创建、管理自动化任务
- **日志查看**: 实时查看系统运行日志
- **性能监控**: 系统资源使用和性能指标

#### 4. 服务模式启动
```bash
# 使用uvicorn直接启动API服务
uvicorn src.web.main:app --host 0.0.0.0 --port 8000 --reload

# 使用gunicorn生产环境部署
gunicorn src.web.main:app -w 4 -k uvicorn.workers.UvicornWorker
```

### 技术栈总览

#### 后端技术栈
- **Python 3.13+**: 主要开发语言，支持JIT编译和无GIL并发
- **FastAPI**: 现代化Web框架，自动API文档生成
- **APScheduler**: 高级任务调度器，支持多种调度策略
- **OpenCV**: 计算机视觉和图像处理
- **EasyOCR**: 多语言OCR文字识别，优化日语支持
- **PyTorch**: 深度学习框架，用于AI决策算法
- **PyDirectInput**: 底层输入模拟，防检测优化
- **PyYAML**: 配置文件解析和管理

#### 前端技术栈
- **Vue.js 3**: 渐进式JavaScript框架
- **Element Plus**: 企业级UI组件库
- **Vite**: 现代化前端构建工具
- **Socket.IO**: 实时双向通信
- **ECharts**: 数据可视化图表库
- **Monaco Editor**: 代码编辑器，用于配置文件编辑
- **Axios**: HTTP客户端库
- **Pinia**: Vue状态管理库

#### 开发工具
- **ESLint + Prettier**: 代码规范和格式化
- **TypeScript**: 类型安全的JavaScript
- **Pytest**: Python单元测试框架
- **Vitest**: 前端单元测试框架

## 开发计划

项目采用6个阶段的开发计划：

1. **阶段1**: 项目基础架构搭建 ✅
2. **阶段2**: 感知模块开发 🚧
3. **阶段3**: 行动模块开发 📋
4. **阶段4**: 决策模块开发 📋
5. **阶段5**: 任务调度系统开发 📋
6. **阶段6**: 系统集成与测试 📋

详细开发计划请参考 [开发实施计划](Gakumasu-Bot开发实施计划.md)

## 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 免责声明

- 本程序仅供学习和研究使用
- 使用本程序的风险由用户自行承担
- 请遵守游戏服务条款和相关法律法规
- 作者不对使用本程序造成的任何后果负责

## 联系方式

- 项目主页: [GitHub Repository](https://github.com/your-repo/gakumasu-bot)
- 问题反馈: [Issues](https://github.com/your-repo/gakumasu-bot/issues)
- 讨论交流: [Discussions](https://github.com/your-repo/gakumasu-bot/discussions)

## 致谢

感谢以下开源项目的支持：
- [OpenCV](https://opencv.org/) - 计算机视觉库
- [EasyOCR](https://github.com/JaidedAI/EasyOCR) - OCR文字识别
- [Ultralytics](https://github.com/ultralytics/ultralytics) - YOLO目标检测
- [PyDirectInput](https://github.com/learncodebygaming/pydirectinput) - 输入模拟

---

**注意**: 本项目目前处于开发阶段，功能尚未完整。请关注项目更新获取最新进展。
